#include <stdio.h>
#include <stdlib.h>

int main() {
    int *opcao = (int*)malloc(sizeof(int));
    char tabuleiro[4][4];
    char *jogador = (char*)malloc(sizeof(char));
    int *linha = (int*)malloc(sizeof(int));
    int *coluna = (int*)malloc(sizeof(int));
    int *casasPreenchidas = (int*)malloc(sizeof(int));

    do {
        printf("1) Jogar\n");
        printf("2) Sair\n");
        scanf("%d", opcao);

        if (*opcao == 1) {
            int i, j;
            for (i = 0; i < 4; i++) {
                for (j = 0; j < 4; j++) {
                    *(*(tabuleiro + i) + j) = ' ';
                }
            }

            *jogador = 'X';
            *casasPreenchidas = 0;

            while (*casasPreenchidas < 16) {
                printf("\nTabuleiro:\n");
                for (i = 0; i < 4; i++) {
                    for (j = 0; j < 4; j++) {
                        printf(" %c ", *(*(tabuleiro + i) + j));
                        if (j < 3) printf("|");
                    }
                    printf("\n");
                    if (i < 3) printf("-----------\n");
                }

                printf("Jogador %c, digite linha (0-3): ", *jogador);
                scanf("%d", linha);
                printf("Digite coluna (0-3): ", *jogador);
                scanf("%d", coluna);

                if (*(*(tabuleiro + *linha) + *coluna) != ' ') {
                    printf("Casa ja preenchida, tente novamente.\n");
                } else {
                    *(*(tabuleiro + *linha) + *coluna) = *jogador;
                    (*casasPreenchidas)++;

                    if (*jogador == 'X') {
                        *jogador = 'O';
                    } else {
                        *jogador = 'X';
                    }
                }
            }

            printf("\nTabuleiro final:\n");
            for (i = 0; i < 4; i++) {
                for (j = 0; j < 4; j++) {
                    printf(" %c ", *(*(tabuleiro + i) + j));
                    if (j < 3) printf("|");
                }
                printf("\n");
                if (i < 3) printf("-----------\n");
            }
            printf("Jogo finalizado!\n");
        }

    } while (*opcao != 2);

    return 0;
}
